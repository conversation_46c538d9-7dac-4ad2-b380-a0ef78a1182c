# 📄 Настройка PDF генерации в Make.com

## 🎯 Как работает PDF генерация

### **Текущий процесс:**
1. **Пользователь** заполняет ROI калькулятор
2. **Отмечает чекбокс** "Email me a detailed PDF report"
3. **Данные отправляются** в Make.com через webhook
4. **Make.com получает** готовый HTML контент для PDF
5. **Make.com генерирует PDF** и отправляет по email

## 📊 **Структура данных в Make.com**

### **Основные поля:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>", 
  "phone": "+1234567890",
  "wantsPdfReport": true,
  "calculationResults": {
    "deltaRevenue": 15000,
    "gainPercent": 30,
    "savedHours": 45
  }
}
```

### **PDF данные (если wantsPdfReport = true):**
```json
{
  "pdfData": {
    "htmlContent": "<!DOCTYPE html>...",
    "fileName": "ROI_Report_John_Doe_2025-07-14.pdf",
    "subject": "Your ROI Calculation Report - $15,000 Monthly Increase Potential"
  }
}
```

## 🛠️ **Настройка Make.com сценария**

### **Шаг 1: Webhook (уже настроен)**
- URL: `https://hook.eu1.make.com/9qtsqgumbg91vc5llliahsy1k41tfgl3`
- Получает данные от ROI калькулятора

### **Шаг 2: Условие (Router)**
```
IF wantsPdfReport = true
  → Генерировать PDF
ELSE
  → Только уведомление менеджеру
```

### **Шаг 3: HTML to PDF модуль**
**Настройки:**
- **HTML Content**: `{{pdfData.htmlContent}}`
- **Page Format**: A4
- **Orientation**: Portrait
- **Margin**: 20px all sides
- **Print Background**: Yes

### **Шаг 4: Email модуль**
**Настройки:**
- **To**: `{{email}}`
- **Subject**: `{{pdfData.subject}}`
- **Body**: 
```html
Привет {{name}}!

Спасибо за интерес к автоматизации! 

Во вложении ваш персональный ROI отчет с расчетом потенциального увеличения дохода на ${{calculationResults.deltaRevenue}}/месяц.

Основные результаты:
• Увеличение дохода: ${{calculationResults.deltaRevenue}}/месяц
• Рост конверсии: +{{calculationResults.gainPercent}}%
• Экономия времени: {{calculationResults.savedHours}} часов/месяц

Готовы обсудить внедрение? Ответьте на это письмо или свяжитесь с нами:
📧 <EMAIL>
📱 Telegram/WhatsApp: [ваш номер]

С уважением,
Команда Setmee
```
- **Attachments**: PDF файл из предыдущего шага
- **Attachment Name**: `{{pdfData.fileName}}`

### **Шаг 5: Уведомление менеджеру**
**Отдельный email для команды:**
- **To**: <EMAIL>
- **Subject**: `Новая заявка ROI калькулятор: {{name}} ({{businessData.niche}})`
- **Body**:
```html
Новая заявка с ROI калькулятора:

👤 Контакт:
• Имя: {{name}}
• Email: {{email}}
• Телефон: {{phone}}
• Комментарий: {{comment}}

📊 Бизнес данные:
• Отрасль: {{businessData.niche}}
• Менеджеров: {{businessData.managers}}
• Каналы: {{businessData.channels}}
• Лидов/месяц: {{businessData.leads}}
• Конверсия: {{businessData.conversion}}%
• Средний чек: ${{businessData.avgDealSize}}

💰 Результаты расчета:
• Потенциальный доход: ${{calculationResults.deltaRevenue}}/месяц
• Рост конверсии: +{{calculationResults.gainPercent}}%
• Экономия времени: {{calculationResults.savedHours}} часов/месяц

📄 PDF отчет: {{wantsPdfReport ? "Отправлен клиенту" : "Не запрашивался"}}

🔗 UTM данные: {{utmData}}
📅 Дата: {{timestamp}}
```

## 🧪 **Тестирование**

### **Тест 1: Проверка webhook**
```bash
curl -X POST http://localhost:3000/api/test-webhook \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>"}'
```

### **Тест 2: Полная заявка с PDF**
1. Откройте http://localhost:3000/roi-calculator
2. Заполните форму
3. Нажмите "Calculate ROI"
4. В модальном окне отметьте "Email me a detailed PDF report"
5. Отправьте заявку
6. Проверьте Make.com на получение данных

## 📋 **Чек-лист настройки**

### ✅ **В Make.com:**
- [ ] Webhook настроен и активен
- [ ] Router с условием `wantsPdfReport = true`
- [ ] HTML to PDF модуль настроен
- [ ] Email клиенту с PDF вложением
- [ ] Email менеджеру с уведомлением
- [ ] Тестирование всех веток сценария

### ✅ **В коде:**
- [x] HTML контент генерируется автоматически
- [x] Персонализация по отрасли
- [x] Форматирование чисел
- [x] Имя файла с датой
- [x] Тема письма с суммой

## 🎨 **Кастомизация PDF**

### **Изменение дизайна:**
Отредактируйте функцию `generatePdfHtml()` в `/src/app/api/send-lead/route.ts`

### **Добавление логотипа:**
```html
<div class="header">
  <img src="https://your-domain.com/logo.png" alt="Logo" style="max-width: 200px;">
  <h1>ROI Calculation Report</h1>
</div>
```

### **Дополнительные секции:**
- Сравнение с конкурентами
- Детальный план внедрения
- Кейсы клиентов
- Калькулятор окупаемости

## 🚀 **Результат**

После настройки пользователи будут получать:
1. **Персональный PDF отчет** с их расчетами
2. **Отраслевые рекомендации** 
3. **Следующие шаги** для внедрения
4. **Контактную информацию** для связи

А вы будете получать **детальные уведомления** о каждой заявке с полной информацией для последующей работы с лидом.
