import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface IntegrationService {
  id: string;
  name: string;
  icon: string;
  description: string;
  category: string;
}

interface IntegrationDiagramProps {
  className?: string;
  animated?: boolean;
}

const IntegrationDiagram: React.FC<IntegrationDiagramProps> = ({
  className,
  animated = true
}) => {
  const integrationServices: IntegrationService[] = [
    { id: 'google', name: 'Google Workspace', icon: '📊', description: 'Docs & Sheets sync', category: 'Productivity' },
    { id: 'telegram', name: 'Telegram', icon: '💬', description: 'Instant notifications', category: 'Communication' },
    { id: 'whatsapp', name: 'WhatsApp', icon: '📱', description: 'Customer messaging', category: 'Communication' },
    { id: 'email', name: 'Email Marketing', icon: '📧', description: 'Campaign automation', category: 'Marketing' },
    { id: 'website', name: 'Website Forms', icon: '🌐', description: 'Lead capture', category: 'Web' },
    { id: 'payments', name: 'Payment Systems', icon: '💳', description: 'Transaction tracking', category: 'Finance' },
    { id: 'analytics', name: 'Analytics', icon: '📈', description: 'Performance insights', category: 'Data' },
    { id: 'docs', name: 'Documents', icon: '📄', description: 'Contract generation', category: 'Productivity' }
  ];

  return (
    <FadeInSection className={cn('relative', className)}>
      <div className="relative w-full bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl p-8 border border-gray-200 shadow-lg overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: 'radial-gradient(circle at 2px 2px, rgba(59, 130, 246, 0.4) 1px, transparent 0)',
            backgroundSize: '30px 30px'
          }} />
        </div>

        {/* Marketing headline */}
        <div className="text-center mb-12 relative z-10">
          <h3 className="text-2xl font-bold text-gray-900 mb-2">
            Kommo CRM: <span className="text-blue-600">Your Business Command Center</span>
          </h3>
          <p className="text-gray-600">
            All your tools work together. Data flows automatically. Nothing falls through the cracks.
          </p>
        </div>

        {/* Integration Flow Diagram */}
        <div className="relative max-w-4xl mx-auto">
          {/* Top row - Input sources */}
          <div className="grid grid-cols-4 gap-4 mb-8">
            {integrationServices.slice(0, 4).map((service, index) => (
              <FadeInSection key={service.id} delay={200 + index * 150}>
                <div className="text-center">
                  <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border-2 border-gray-200 mx-auto mb-2 hover:border-blue-300 transition-colors">
                    <span className="text-2xl">{service.icon}</span>
                  </div>
                  <div className="text-xs font-semibold text-gray-900">{service.name}</div>
                  <div className="text-xs text-gray-600">{service.description}</div>
                </div>
              </FadeInSection>
            ))}
          </div>

          {/* Connection arrows pointing down */}
          <div className="flex justify-center mb-8">
            <div className="grid grid-cols-4 gap-4 w-full max-w-md">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="flex justify-center">
                  <div className="w-px h-8 bg-gradient-to-b from-blue-300 to-blue-500"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Central Kommo CRM Hub */}
          <FadeInSection delay={800} className="flex justify-center mb-8">
            <div className="relative">
              <div className="w-32 h-32 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white shadow-2xl">
                <div className="text-center">
                  <div className="text-4xl mb-2">🎯</div>
                  <div className="font-bold text-lg">Kommo CRM</div>
                  <div className="text-blue-100 text-sm">Central Hub</div>
                </div>
              </div>
              {/* Pulsing effect */}
              {animated && (
                <div className="absolute inset-0 bg-blue-400 rounded-xl animate-ping opacity-20"></div>
              )}
            </div>
          </FadeInSection>

          {/* Connection arrows pointing down */}
          <div className="flex justify-center mb-8">
            <div className="grid grid-cols-4 gap-4 w-full max-w-md">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="flex justify-center">
                  <div className="w-px h-8 bg-gradient-to-b from-blue-500 to-blue-300"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Bottom row - Output destinations */}
          <div className="grid grid-cols-4 gap-4">
            {integrationServices.slice(4, 8).map((service, index) => (
              <FadeInSection key={service.id} delay={1200 + index * 150}>
                <div className="text-center">
                  <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border-2 border-gray-200 mx-auto mb-2 hover:border-blue-300 transition-colors">
                    <span className="text-2xl">{service.icon}</span>
                  </div>
                  <div className="text-xs font-semibold text-gray-900">{service.name}</div>
                  <div className="text-xs text-gray-600">{service.description}</div>
                </div>
              </FadeInSection>
            ))}
          </div>
        </div>

        {/* Marketing benefits below */}
        <FadeInSection delay={1500} className="mt-12 text-center relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg p-4 shadow-md border border-gray-200">
              <div className="text-2xl mb-2">⚡</div>
              <h4 className="font-semibold text-gray-900 text-sm mb-1">Instant Data Sync</h4>
              <p className="text-xs text-gray-600">Information updates across all platforms in real-time</p>
            </div>
            <div className="bg-white rounded-lg p-4 shadow-md border border-gray-200">
              <div className="text-2xl mb-2">🔄</div>
              <h4 className="font-semibold text-gray-900 text-sm mb-1">Automated Workflows</h4>
              <p className="text-xs text-gray-600">Triggers and actions happen without manual intervention</p>
            </div>
            <div className="bg-white rounded-lg p-4 shadow-md border border-gray-200">
              <div className="text-2xl mb-2">📊</div>
              <h4 className="font-semibold text-gray-900 text-sm mb-1">Unified Analytics</h4>
              <p className="text-xs text-gray-600">Complete business overview from one dashboard</p>
            </div>
          </div>
        </FadeInSection>

        {/* Call to action */}
        <FadeInSection delay={1800} className="mt-8 text-center relative z-10">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-center justify-center mb-3">
              <span className="text-blue-500 text-xl mr-2">🚀</span>
              <span className="font-bold text-gray-900">Ready to connect your business ecosystem?</span>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              We'll map out your current tools and create a custom integration strategy that saves time and eliminates data silos.
            </p>
            <div className="inline-flex items-center text-blue-600 font-semibold text-sm">
              <span>Get your integration roadmap</span>
              <span className="ml-1">→</span>
            </div>
          </div>
        </FadeInSection>
      </div>
    </FadeInSection>
  );
};

export default IntegrationDiagram;
