import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface IntegrationService {
  id: string;
  name: string;
  icon: string;
  category: string;
  color: string;
}

interface IntegrationDiagramProps {
  className?: string;
  animated?: boolean;
}

const IntegrationDiagram: React.FC<IntegrationDiagramProps> = ({
  className,
  animated = true
}) => {
  const integrationServices: IntegrationService[] = [
    { id: 'google', name: 'Google Workspace', icon: '📊', category: 'Productivity', color: 'bg-green-100 text-green-600 border-green-200' },
    { id: 'telegram', name: 'Telegram', icon: '💬', category: 'Communication', color: 'bg-blue-100 text-blue-600 border-blue-200' },
    { id: 'whatsapp', name: 'WhatsApp', icon: '📱', category: 'Communication', color: 'bg-green-100 text-green-600 border-green-200' },
    { id: 'email', name: 'Email', icon: '📧', category: 'Communication', color: 'bg-red-100 text-red-600 border-red-200' },
    { id: 'website', name: 'Website', icon: '🌐', category: 'Web', color: 'bg-purple-100 text-purple-600 border-purple-200' },
    { id: 'payments', name: 'Payments', icon: '💳', category: 'Finance', color: 'bg-yellow-100 text-yellow-600 border-yellow-200' },
    { id: 'analytics', name: 'Analytics', icon: '📈', category: 'Data', color: 'bg-indigo-100 text-indigo-600 border-indigo-200' },
    { id: 'docs', name: 'Documents', icon: '📄', category: 'Productivity', color: 'bg-gray-100 text-gray-600 border-gray-200' }
  ];

  return (
    <FadeInSection className={cn('relative', className)}>
      <div className="bg-white rounded-xl border border-gray-200 p-8 shadow-lg">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full mb-4">
            <span className="text-white text-2xl">🔗</span>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">Make.com Integration Hub</h3>
          <p className="text-gray-600">Connect Kommo CRM with 1000+ services</p>
        </div>

        {/* Central Kommo CRM */}
        <div className="flex justify-center mb-8">
          <FadeInSection delay={200}>
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white shadow-xl">
                <div className="text-center">
                  <div className="text-3xl mb-2">🎯</div>
                  <h4 className="text-lg font-bold">Kommo CRM</h4>
                  <p className="text-blue-100 text-sm">Central Hub</p>
                </div>
              </div>
              {/* Pulse animation */}
              {animated && (
                <div className="absolute inset-0 bg-blue-400 rounded-xl animate-ping opacity-20"></div>
              )}
            </div>
          </FadeInSection>
        </div>

        {/* Connection Lines */}
        <div className="relative mb-8">
          <div className="absolute left-1/2 top-0 w-px h-8 bg-gradient-to-b from-blue-300 to-transparent transform -translate-x-1/2"></div>
          <div className="absolute left-1/2 bottom-0 w-px h-8 bg-gradient-to-t from-blue-300 to-transparent transform -translate-x-1/2"></div>
          <div className="absolute top-1/2 left-0 h-px w-8 bg-gradient-to-r from-blue-300 to-transparent transform -translate-y-1/2"></div>
          <div className="absolute top-1/2 right-0 h-px w-8 bg-gradient-to-l from-blue-300 to-transparent transform -translate-y-1/2"></div>
        </div>

        {/* Integration Services Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {integrationServices.map((service, index) => (
            <FadeInSection key={service.id} delay={300 + index * 100}>
              <div className={cn(
                'bg-white border-2 rounded-lg p-4 text-center transition-all duration-300 hover:shadow-lg hover:-translate-y-1',
                service.color,
                animated && 'hover:scale-105'
              )}>
                <div className="text-2xl mb-2">{service.icon}</div>
                <h5 className="font-semibold text-sm mb-1">{service.name}</h5>
                <p className="text-xs opacity-75">{service.category}</p>
              </div>
            </FadeInSection>
          ))}
        </div>

        {/* Bottom CTA */}
        <FadeInSection delay={1200} className="mt-8 text-center">
          <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-6">
            <div className="flex items-center justify-center mb-3">
              <span className="text-purple-500 text-xl mr-2">🚀</span>
              <span className="font-semibold text-gray-900">And 1000+ more services</span>
            </div>
            <p className="text-gray-600 text-sm">
              From CRM and email marketing to accounting and project management -
              we can integrate virtually any business tool with your Kommo CRM.
            </p>
          </div>
        </FadeInSection>
      </div>
    </FadeInSection>
  );
};

export default IntegrationDiagram;
