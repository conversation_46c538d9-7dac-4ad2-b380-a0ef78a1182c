import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface IntegrationFlow {
  id: string;
  title: string;
  description: string;
  tools: string[];
  benefit: string;
  icon: string;
}

interface IntegrationDiagramProps {
  className?: string;
  animated?: boolean;
}

const IntegrationDiagram: React.FC<IntegrationDiagramProps> = ({
  className,
  animated = true
}) => {
  const integrationFlows: IntegrationFlow[] = [
    {
      id: 'lead-capture',
      title: 'Lead Capture & Nurturing',
      description: 'Automatic lead collection from multiple channels with instant follow-up',
      tools: ['Website Forms', 'Facebook Ads', 'Google Ads', 'Landing Pages'],
      benefit: '3x faster lead response time',
      icon: '🎯'
    },
    {
      id: 'communication',
      title: 'Omnichannel Communication',
      description: 'Unified messaging across all customer touchpoints',
      tools: ['WhatsApp', 'Telegram', 'Email', 'SMS', 'Voice Calls'],
      benefit: '85% customer satisfaction rate',
      icon: '💬'
    },
    {
      id: 'sales-automation',
      title: 'Sales Process Automation',
      description: 'Streamlined pipeline management with automated workflows',
      tools: ['Payment Systems', 'Invoicing', 'Contracts', 'Analytics'],
      benefit: '40% increase in conversion',
      icon: '⚡'
    },
    {
      id: 'analytics',
      title: 'Business Intelligence',
      description: 'Real-time insights and performance tracking across all channels',
      tools: ['Google Analytics', 'Call Tracking', 'Revenue Reports', 'ROI Analysis'],
      benefit: 'Data-driven decisions',
      icon: '📊'
    }
  ];

  return (
    <FadeInSection className={cn('relative', className)}>
      <div className="relative w-full bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl p-8 border border-gray-200 shadow-lg overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: 'radial-gradient(circle at 2px 2px, rgba(59, 130, 246, 0.4) 1px, transparent 0)',
            backgroundSize: '30px 30px'
          }} />
        </div>

        {/* Marketing headline */}
        <div className="text-center mb-12 relative z-10">
          <h3 className="text-2xl font-bold text-gray-900 mb-2">
            <span className="text-blue-600">Powerful Integrations</span> That Transform Your Business
          </h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Connect your favorite tools with Kommo CRM to create seamless workflows that save time, reduce errors, and boost productivity across your entire organization.
          </p>
        </div>

        {/* Integration Flow Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto relative z-10">
          {integrationFlows.map((flow, index) => (
            <FadeInSection key={flow.id} delay={200 + index * 200}>
              <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 group">
                {/* Header */}
                <div className="flex items-start mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white text-2xl mr-4 group-hover:scale-110 transition-transform">
                    {flow.icon}
                  </div>
                  <div className="flex-1">
                    <h4 className="text-lg font-bold text-gray-900 mb-1">{flow.title}</h4>
                    <p className="text-sm text-gray-600">{flow.description}</p>
                  </div>
                </div>

                {/* Tools integration visualization */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {flow.tools.map((tool, toolIndex) => (
                      <span
                        key={toolIndex}
                        className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full border"
                      >
                        {tool}
                      </span>
                    ))}
                  </div>

                  {/* Connection visualization */}
                  <div className="flex items-center justify-center py-2">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                      <div className="w-8 h-px bg-gradient-to-r from-blue-400 to-blue-600"></div>
                      <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded flex items-center justify-center text-white text-xs font-bold">K</div>
                      <div className="w-8 h-px bg-gradient-to-r from-blue-600 to-green-500"></div>
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    </div>
                  </div>
                </div>

                {/* Benefit */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <div className="flex items-center">
                    <span className="text-green-600 text-sm mr-2">✓</span>
                    <span className="text-green-800 font-semibold text-sm">{flow.benefit}</span>
                  </div>
                </div>
              </div>
            </FadeInSection>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-12 relative z-10">
          <div className="bg-white rounded-lg p-8 shadow-lg border border-gray-200 max-w-2xl mx-auto">
            <h4 className="text-xl font-bold text-gray-900 mb-3">Transform Your Business Operations Today</h4>
            <p className="text-gray-600 mb-6">
              Stop losing leads and wasting time on manual tasks. Our integration experts will connect all your tools with Kommo CRM to create a seamless, automated workflow that grows your business.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Free Integration Audit
              </button>
              <button className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
                View Case Studies
              </button>
            </div>
          </div>
        </div>
      </div>
    </FadeInSection>
  );
};

export default IntegrationDiagram;
