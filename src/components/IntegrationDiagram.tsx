import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface IntegrationNode {
  id: string;
  name: string;
  icon: string;
  position: { x: number; y: number };
  description: string;
}

interface IntegrationDiagramProps {
  className?: string;
  animated?: boolean;
}

const IntegrationDiagram: React.FC<IntegrationDiagramProps> = ({
  className,
  animated = true
}) => {
  const nodes: IntegrationNode[] = [
    // Center - Kommo CRM (the hub)
    {
      id: 'kommo',
      name: 'Kommo CRM',
      icon: '🎯',
      position: { x: 50, y: 50 },
      description: 'Your Business Hub'
    },
    // Surrounding integrations in a circle
    {
      id: 'google',
      name: 'Google Workspace',
      icon: '📊',
      position: { x: 50, y: 20 },
      description: 'Docs & Sheets sync'
    },
    {
      id: 'telegram',
      name: 'Telegram',
      icon: '💬',
      position: { x: 75, y: 30 },
      description: 'Instant notifications'
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      icon: '📱',
      position: { x: 85, y: 50 },
      description: 'Customer messaging'
    },
    {
      id: 'payments',
      name: 'Payments',
      icon: '💳',
      position: { x: 75, y: 70 },
      description: 'Transaction tracking'
    },
    {
      id: 'analytics',
      name: 'Analytics',
      icon: '📈',
      position: { x: 50, y: 80 },
      description: 'Performance insights'
    },
    {
      id: 'email',
      name: 'Email Marketing',
      icon: '📧',
      position: { x: 25, y: 70 },
      description: 'Campaign automation'
    },
    {
      id: 'website',
      name: 'Website',
      icon: '🌐',
      position: { x: 15, y: 50 },
      description: 'Lead capture forms'
    },
    {
      id: 'docs',
      name: 'Documents',
      icon: '📄',
      position: { x: 25, y: 30 },
      description: 'Contract generation'
    }
  ];

  const connections = [
    { from: 'kommo', to: 'google' },
    { from: 'kommo', to: 'telegram' },
    { from: 'kommo', to: 'whatsapp' },
    { from: 'kommo', to: 'payments' },
    { from: 'kommo', to: 'analytics' },
    { from: 'kommo', to: 'email' },
    { from: 'kommo', to: 'website' },
    { from: 'kommo', to: 'docs' }
  ];

  const getNodeById = (id: string) => nodes.find(node => node.id === id);

  return (
    <FadeInSection className={cn('relative', className)}>
      <div className="relative w-full bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl p-8 border border-gray-200 shadow-lg overflow-hidden">
        {/* Background grid pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: 'radial-gradient(circle at 2px 2px, rgba(59, 130, 246, 0.4) 1px, transparent 0)',
            backgroundSize: '30px 30px'
          }} />
        </div>

        {/* Marketing headline */}
        <div className="text-center mb-8 relative z-10">
          <h3 className="text-2xl font-bold text-gray-900 mb-2">
            Kommo CRM: <span className="text-blue-600">Your Business Command Center</span>
          </h3>
          <p className="text-gray-600">
            All your tools work together. Data flows automatically. Nothing falls through the cracks.
          </p>
        </div>

        {/* Integration diagram container */}
        <div className="relative h-96 mx-auto max-w-2xl">
          {/* Animated connection lines */}
          <svg className="absolute inset-0 w-full h-full" style={{ zIndex: 1 }}>
            {connections.map((connection, index) => {
              const fromNode = getNodeById(connection.from);
              const toNode = getNodeById(connection.to);
              if (!fromNode || !toNode) return null;

              return (
                <g key={index}>
                  <line
                    x1={`${fromNode.position.x}%`}
                    y1={`${fromNode.position.y}%`}
                    x2={`${toNode.position.x}%`}
                    y2={`${toNode.position.y}%`}
                    stroke="rgba(59, 130, 246, 0.3)"
                    strokeWidth="2"
                    className={animated ? 'animate-pulse' : ''}
                  />
                  {animated && (
                    <circle
                      r="3"
                      fill="rgb(59, 130, 246)"
                      opacity="0.6"
                    >
                      <animateMotion
                        dur="3s"
                        repeatCount="indefinite"
                        begin={`${index * 0.3}s`}
                      >
                        <mpath>
                          <path d={`M ${fromNode.position.x} ${fromNode.position.y} L ${toNode.position.x} ${toNode.position.y}`} />
                        </mpath>
                      </animateMotion>
                    </circle>
                  )}
                </g>
              );
            })}
          </svg>

          {/* Integration nodes */}
          {nodes.map((node, index) => (
            <FadeInSection 
              key={node.id} 
              delay={200 + index * 150}
              className={cn(
                'absolute transform -translate-x-1/2 -translate-y-1/2 transition-all duration-300',
                animated && 'hover:scale-110'
              )}
              style={{
                left: `${node.position.x}%`,
                top: `${node.position.y}%`,
                zIndex: node.id === 'kommo' ? 20 : 10
              }}
            >
              {node.id === 'kommo' ? (
                // Central Kommo hub - larger and more prominent
                <div className="text-center group">
                  <div className="relative">
                    <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white shadow-xl ring-4 ring-white ring-opacity-50">
                      <span className="text-3xl">{node.icon}</span>
                    </div>
                    {/* Pulsing ring effect */}
                    {animated && (
                      <div className="absolute inset-0 bg-blue-400 rounded-full animate-ping opacity-30"></div>
                    )}
                  </div>
                  <div className="mt-3 bg-white rounded-lg px-4 py-2 shadow-lg border-2 border-blue-200">
                    <div className="font-bold text-blue-900 text-sm">{node.name}</div>
                    <div className="text-xs text-blue-600 font-medium">{node.description}</div>
                  </div>
                </div>
              ) : (
                // Integration service nodes
                <div className="text-center group">
                  <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg border-2 border-gray-200 group-hover:border-blue-300 transition-colors">
                    <span className="text-2xl">{node.icon}</span>
                  </div>
                  <div className="mt-2 bg-white rounded-lg px-3 py-1 shadow-md border border-gray-200 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="font-semibold text-gray-900 text-xs">{node.name}</div>
                    <div className="text-xs text-gray-600">{node.description}</div>
                  </div>
                </div>
              )}
            </FadeInSection>
          ))}

          {/* Central glow effect */}
          <div 
            className="absolute w-32 h-32 bg-blue-400 rounded-full opacity-10 blur-2xl"
            style={{
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 0
            }}
          />
        </div>

        {/* Marketing benefits below */}
        <FadeInSection delay={1500} className="mt-12 text-center relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg p-4 shadow-md border border-gray-200">
              <div className="text-2xl mb-2">⚡</div>
              <h4 className="font-semibold text-gray-900 text-sm mb-1">Instant Data Sync</h4>
              <p className="text-xs text-gray-600">Information updates across all platforms in real-time</p>
            </div>
            <div className="bg-white rounded-lg p-4 shadow-md border border-gray-200">
              <div className="text-2xl mb-2">🔄</div>
              <h4 className="font-semibold text-gray-900 text-sm mb-1">Automated Workflows</h4>
              <p className="text-xs text-gray-600">Triggers and actions happen without manual intervention</p>
            </div>
            <div className="bg-white rounded-lg p-4 shadow-md border border-gray-200">
              <div className="text-2xl mb-2">📊</div>
              <h4 className="font-semibold text-gray-900 text-sm mb-1">Unified Analytics</h4>
              <p className="text-xs text-gray-600">Complete business overview from one dashboard</p>
            </div>
          </div>
        </FadeInSection>

        {/* Call to action */}
        <FadeInSection delay={1800} className="mt-8 text-center relative z-10">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-center justify-center mb-3">
              <span className="text-blue-500 text-xl mr-2">🚀</span>
              <span className="font-bold text-gray-900">Ready to connect your business ecosystem?</span>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              We'll map out your current tools and create a custom integration strategy that saves time and eliminates data silos.
            </p>
            <div className="inline-flex items-center text-blue-600 font-semibold text-sm">
              <span>Get your integration roadmap</span>
              <span className="ml-1">→</span>
            </div>
          </div>
        </FadeInSection>
      </div>
    </FadeInSection>
  );
};

export default IntegrationDiagram;
